@import "tailwindcss";

:root {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --aisin-blue-primary: #1265AE;
  --aisin-blue-secondary: #08599FCC;
  --aisin-green: #16A34A;
  --aisin-red: #FD7A88;
  --aisin-yellow: #FFD778;
  --aisin-purple: #C08BFF;
  --aisin-font-family: 'Manrope', sans-serif;
  --aisin-image-border-radius: 4px;
  
  --aisin-container-border-radius: 12px;
  --aisin-container-padding: 16px;
  --aisin-container-margin: 16px;
  --aisin-container-background-color: #fff;
  --aisin-container-border: 1px solid #E5E7EB;

  --aisin-container-title-font-size: 16px;
  --aisin-container-title-font-weight: 600;
  --aisin-container-title-font-color: #FFFFFF;
  --aisin-container-title-font-family: '<PERSON>unito Sans', sans-serif;
  --aisin-container-title-letter-spacing: 0%;

  --aisin-form-field-border-radius: 12px;
  --aisin-label-title-font-family: 'Nunito Sans', sans-serif;
  --aisin-label-font-size: 16px;
  --aisin-label-font-color: #131313;
  --aisin-label-font-weight: 400;

}
*{
  font-family: var(--aisin-font-family);
}
label{
  font-family: var(--aisin-label-title-font-family);
  font-size: var(--aisin-label-font-size);
  font-weight: var(--aisin-label-font-weight);
  letter-spacing: 0%;
  color: var(--aisin-label-font-color);
}
 

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
  width: 11px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 26, 114, 0.20) !important;
  border-radius: 12px !important;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 26, 114, 0.20);
}

::-webkit-scrollbar-track-piece {
  background: rgba(0, 26, 114, 0.20);
  border-radius: 12px;
}

::-webkit-scrollbar-thumb {
  background: #042174;
  border-radius: 12px;
  border: 2px solid rgba(0, 26, 114, 0.20);
}

::-webkit-scrollbar-thumb:hover {
  background: #042174;
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: #042174 transparent;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #042174;
    background-color: #ffffff;
  }

}
