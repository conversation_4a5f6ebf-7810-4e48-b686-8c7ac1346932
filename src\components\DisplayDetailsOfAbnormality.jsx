// DisplayDetailsOfAbnormality.jsx
import React from "react";
import closeIcon from "../assets/icons/Close.svg";
import ImageDisplay from "./ImageDisplay";
import CoordinateDetails from "./CoordinateDetails";

const DisplayDetailsOfAbnormality = ({ onClose }) => {
  return (
    <div className="w-[456px]  h-full flex flex-col p-3 ml-[90px]">
      <div className="flex justify-between items-center w-full border-b border-[#E5E5E5] pb-3">
        <p className="text-[#042174] text-[18px] font-[Nunito Sans] font-semibold">
          Display Details of Abnormality
        </p>
        <img
          src={closeIcon}
          className="w-6 h-6 cursor-pointer"
          alt="cross mark"
          onClick={onClose}
        />
      </div>
      <div className="overflow-y-auto p-3 grow">
        <ImageDisplay />
        <CoordinateDetails />
      </div>
    </div>
  );
};

export default DisplayDetailsOfAbnormality;
