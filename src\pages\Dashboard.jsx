// Dashboard.jsx (or any parent component)
import React, { useState } from "react";
import Drawer from "react-modern-drawer";
import "react-modern-drawer/dist/index.css";
import DisplayDetailsOfAbnormality from "../components/DisplayDetailsOfAbnormality";
import FilterComponent from "../components/forms/FilterComponent";

const Dashboard = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const toggleDrawer = () => {
    setIsDrawerOpen((prev) => !prev);
  };

  return (
    <div>
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40">
        <FilterComponent />
      </div>
      <button
        onClick={toggleDrawer}
        className="bg-blue-600 text-white px-4 py-2 rounded"
      >
        Show Abnormality Details
      </button>

      <Drawer
        open={isDrawerOpen}
        onClose={toggleDrawer}
        direction="left"
        className="drawer"
        size={536}
      >
        <DisplayDetailsOfAbnormality onClose={toggleDrawer} />
      </Drawer>
      <div className="dashboard-container relative"></div>
    </div>
  );
};

export default Dashboard;
